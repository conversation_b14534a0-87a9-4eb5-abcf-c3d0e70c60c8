import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:simple_pip_mode/simple_pip.dart';
import 'package:flutter_hls_parser/flutter_hls_parser.dart'
    as hls_parser; // Keep if needed for quality switching logic
import 'package:http/http.dart' as http;
import 'package:cat_tv/custom_player/custom_player_skin.dart';

class PlayerPage extends StatefulWidget {
  final String channelUrl;
  const PlayerPage({super.key, required this.channelUrl});

  @override
  State<PlayerPage> createState() => _PlayerPageState();
}

class _PlayerPageState extends State<PlayerPage> with TickerProviderStateMixin {
  late final Player player = Player();
  late final VideoController controller = VideoController(player);

  // Player state variables
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _currentVolume = 100.0;
  double _previousVolume = 100.0;
  bool _isPlaying = false;
  bool _isBuffering = false;
  bool _hasSource = false;
  String? _errorMessage;
  final List<hls_parser.Variant> _availableQualities = [];
  int? _bufferingPercentage; // New: for buffering percentage
  bool _isLiveStream = false; // New: for live stream indicator

  // UI state variables
  bool _isFullScreen = false; // Initialized directly to false
  bool _isVolumeSliderVisible = false;
  bool _areControlsVisible = false;
  Timer? _hideControlsTimer;

  // Additional features state
  double _playbackSpeed = 1.0;
  String? _currentQuality;
  String? _currentSubtitle;

  // Animation controllers for live indicator
  late AnimationController _liveAnimationController;
  late Animation<double> _liveAnimation;

  // Splash effects state
  bool _showPlayPauseSplash = false;
  bool _showVolumeSplash = false;
  String _volumeSplashText = '';
  Timer? _splashTimer;

  // Cat sounds feature state
  bool _showCatSound = false;
  String _catSoundText = '';
  Timer? _catSoundTimer;
  final List<String> _catSounds = [
    'Meow!',
    'Purr~',
    'Mrow!',
    'Mew mew',
    'Prrrr',
    'Nya~',
    'Meow meow!',
    'Chirp!',
    'Trill~',
    'Miau!',
  ];

  // Stream subscriptions
  late StreamSubscription<Duration> _positionSubscription;
  late StreamSubscription<Duration> _durationSubscription;
  late StreamSubscription<double> _volumeSubscription;
  late StreamSubscription<bool> _playingSubscription;
  late StreamSubscription<bool> _bufferingSubscription;
  late StreamSubscription<String> _errorSubscription;
  late StreamSubscription<Playlist> _playlistSubscription;
  late StreamSubscription<Duration>
  _bufferSubscription; // New: for buffering percentage

  @override
  void initState() {
    super.initState();
    // Removed: _isFullScreen = false; // Initial state for player page
    // Removed: player.stream.fullscreen.listen((isFullScreen) { ... });

    // Initialize animation controller for live indicator
    _liveAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _liveAnimation = Tween<double>(begin: 0.3, end: 1.0).animate(
      CurvedAnimation(
        parent: _liveAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // Start hide controls timer initially
    _startHideControlsTimer();

    // Listen to player streams
    _positionSubscription = player.stream.position.listen((position) {
      setState(() {
        _currentPosition = position;
      });
    });
    _durationSubscription = player.stream.duration.listen((duration) {
      setState(() {
        _totalDuration = duration;
      });
    });
    _volumeSubscription = player.stream.volume.listen((volume) {
      setState(() {
        _currentVolume = volume;
      });
    });
    _playingSubscription = player.stream.playing.listen((playing) {
      setState(() {
        _isPlaying = playing;
      });
    });
    _bufferingSubscription = player.stream.buffering.listen((buffering) {
      setState(() {
        _isBuffering = buffering;
      });
    });
    _errorSubscription = player.stream.error.listen((error) {
      setState(() {
        _errorMessage = error;
      });
    });
    _playlistSubscription = player.stream.playlist.listen((playlist) {
      setState(() {
        _hasSource = playlist.medias.isNotEmpty;
        if (_hasSource) {
          // Determine if it's a live stream (simple check, can be improved)
          _isLiveStream = playlist.medias.first.uri.contains(
            'm3u8',
          ); // Example heuristic
        } else {
          _isLiveStream = false;
        }
      });
    });
    _bufferSubscription = player.stream.buffer.listen((buffer) {
      setState(() {
        if (_totalDuration.inMilliseconds > 0) {
          _bufferingPercentage =
              (buffer.inMilliseconds / _totalDuration.inMilliseconds * 100)
                  .round();
        } else {
          _bufferingPercentage =
              null; // No percentage for live streams or unknown duration
        }
      });
    });

    // Load the channel URL passed from the previous page
    _loadUrl(widget.channelUrl);
  }

  @override
  void didUpdateWidget(PlayerPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Handle live stream state changes for animation
    // This logic is now correctly placed here.
    if (_isLiveStream) {
      _liveAnimationController.repeat(reverse: true);
    } else {
      _liveAnimationController.stop();
      _liveAnimationController.reset();
    }
    // If the channel URL changes, load the new one
    if (widget.channelUrl != oldWidget.channelUrl) {
      _loadUrl(widget.channelUrl);
    }
  }

  @override
  void dispose() {
    _positionSubscription.cancel();
    _durationSubscription.cancel();
    _volumeSubscription.cancel();
    _playingSubscription.cancel();
    _bufferingSubscription.cancel();
    _errorSubscription.cancel();
    _playlistSubscription.cancel();
    _bufferSubscription.cancel(); // Cancel new subscription
    _hideControlsTimer?.cancel();
    _splashTimer?.cancel();
    _catSoundTimer?.cancel();
    _liveAnimationController.dispose();
    player.dispose();
    super.dispose();
  }

  Future<void> _loadUrl(String url) async {
    if (url.isNotEmpty) {
      setState(() {
        _hasSource = true;
        _isBuffering = true; // Show loading indicator immediately
        _errorMessage = null; // Clear any previous error
        _availableQualities.clear(); // Clear previous qualities
        _currentQuality = null; // Reset current quality
      });

      if (url.endsWith('.m3u8')) {
        try {
          // First, fetch the m3u8 content
          final response = await http.get(Uri.parse(url));
          if (response.statusCode == 200) {
            final playlist = await hls_parser.HlsPlaylistParser.create()
                .parseString(Uri.parse(url), response.body);

            if (playlist is hls_parser.HlsMasterPlaylist) {
              setState(() {
                _availableQualities.addAll(playlist.variants);
                // Sort qualities by resolution or bitrate for better display
                _availableQualities.sort((a, b) {
                  final aRes = (a.format.width ?? 0) * (a.format.height ?? 0);
                  final bRes = (b.format.width ?? 0) * (b.format.height ?? 0);
                  if (aRes != bRes) return aRes.compareTo(bRes);
                  return (a.format.bitrate ?? 0).compareTo(
                    b.format.bitrate ?? 0,
                  );
                });
              });
            }
          } else {
            throw Exception('Failed to fetch m3u8: ${response.statusCode}');
          }
        } catch (e) {
          setState(() {
            _errorMessage = 'Failed to parse HLS stream: $e';
            _isBuffering = false;
          });
          player.stop();
          return;
        }
      }

      player.open(Media(url));
    } else {
      player.stop();
      setState(() {
        _hasSource = false;
        _isBuffering = false;
        _errorMessage = null;
        _availableQualities.clear();
        _currentQuality = null;
      });
    }
  }

  void _startHideControlsTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      setState(() {
        _areControlsVisible = false;
      });
    });
  }

  void _enterPictureInPicture() async {
    try {
      await SimplePip().enterPipMode();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Picture-in-Picture not supported on this device'),
          ),
        );
      }
    }
  }

  void _showSubtitlesMenu(BuildContext buttonContext) async {
    final RenderBox renderBox = buttonContext.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    _hideControlsTimer?.cancel();

    await showMenu<String>(
      context: context,
      position: RelativeRect.fromRect(
        Rect.fromLTWH(
          offset.dx,
          offset.dy - (size.height + 8.0),
          size.width,
          size.height,
        ),
        Offset.zero & MediaQuery.of(context).size,
      ),
      items: <PopupMenuEntry<String>>[
        PopupMenuItem<String>(
          value: 'none',
          child: _buildMenuItem('None', _currentSubtitle == null),
          onTap: () {
            setState(() {
              _currentSubtitle = null;
            });
          },
        ),
        PopupMenuItem<String>(
          value: 'en',
          child: _buildMenuItem('English', _currentSubtitle == 'en'),
          onTap: () {
            setState(() {
              _currentSubtitle = 'en';
            });
          },
        ),
        PopupMenuItem<String>(
          value: 'es',
          child: _buildMenuItem('Spanish', _currentSubtitle == 'es'),
          onTap: () {
            setState(() {
              _currentSubtitle = 'es';
            });
          },
        ),
        PopupMenuItem<String>(
          value: 'fr',
          child: _buildMenuItem('French', _currentSubtitle == 'fr'),
          onTap: () {
            setState(() {
              _currentSubtitle = 'fr';
            });
          },
        ),
        PopupMenuItem<String>(
          value: 'de',
          child: _buildMenuItem('German', _currentSubtitle == 'de'),
          onTap: () {
            setState(() {
              _currentSubtitle = 'de';
            });
          },
        ),
      ],
      elevation: 8.0,
    );
    _startHideControlsTimer();
  }

  Widget _buildMenuItem(String title, bool isSelected) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            color: isSelected ? Colors.blue : Colors.black87,
          ),
        ),
        if (isSelected) const Icon(Icons.check, color: Colors.blue, size: 18),
      ],
    );
  }

  void _showSettingsMenu(BuildContext buttonContext) async {
    final RenderBox renderBox = buttonContext.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    _hideControlsTimer?.cancel();

    await showMenu<String>(
      context: context,
      position: RelativeRect.fromRect(
        Rect.fromLTWH(
          offset.dx,
          offset.dy - (size.height + 8.0),
          size.width,
          size.height,
        ),
        Offset.zero & MediaQuery.of(context).size,
      ),
      items: <PopupMenuEntry<String>>[
        PopupMenuItem<String>(
          value: 'speed',
          child: _buildMenuOptionWithArrow('Speed', '${_playbackSpeed}x'),
          onTap: () {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _showSpeedSubmenu(buttonContext);
            });
          },
        ),
        PopupMenuItem<String>(
          value: 'quality',
          child: _buildMenuOptionWithArrow(
            'Quality',
            _currentQuality ?? 'Auto',
          ),
          onTap: () {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _showQualitySubmenu(buttonContext);
            });
          },
        ),
      ],
      elevation: 8.0,
    );
    _startHideControlsTimer();
  }

  Widget _buildMenuOptionWithArrow(String title, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 14, color: Colors.black87),
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              value,
              style: const TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(width: 8),
            const Icon(Icons.arrow_forward_ios, size: 14, color: Colors.grey),
          ],
        ),
      ],
    );
  }

  void _showSpeedSubmenu(BuildContext parentButtonContext) async {
    final RenderBox renderBox =
        parentButtonContext.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    _hideControlsTimer?.cancel();

    await showMenu<double>(
      context: context,
      position: RelativeRect.fromRect(
        Rect.fromLTWH(
          offset.dx,
          offset.dy - (size.height + 8.0),
          size.width,
          size.height,
        ),
        Offset.zero & MediaQuery.of(context).size,
      ),
      items: <PopupMenuEntry<double>>[
        ...[0.5, 0.75, 1.0, 1.25, 1.5, 2.0].map((speed) {
          return PopupMenuItem<double>(
            value: speed,
            child: _buildMenuItem('${speed}x', _playbackSpeed == speed),
            onTap: () {
              setState(() {
                _playbackSpeed = speed;
              });
              player.setRate(speed);
            },
          );
        }),
      ],
      elevation: 8.0,
    );
    _startHideControlsTimer();
  }

  void _showQualitySubmenu(BuildContext parentButtonContext) async {
    final RenderBox renderBox =
        parentButtonContext.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    final Size size = renderBox.size;

    _hideControlsTimer?.cancel();

    await showMenu<String>(
      context: context,
      position: RelativeRect.fromRect(
        Rect.fromLTWH(
          offset.dx,
          offset.dy - (size.height + 8.0),
          size.width,
          size.height,
        ),
        Offset.zero & MediaQuery.of(context).size,
      ),
      items: <PopupMenuEntry<String>>[
        PopupMenuItem<String>(
          value: 'auto',
          child: _buildMenuItem('Auto', _currentQuality == null),
          onTap: () {
            setState(() {
              _currentQuality = null;
            });
          },
        ),
        ..._availableQualities.map((variant) {
          final quality =
              '${variant.format.width ?? 0}x${variant.format.height ?? 0}';
          final bitrate = variant.format.bitrate;
          final bitrateText =
              bitrate != null ? ' (${(bitrate / 1000).round()} kbps)' : '';
          return PopupMenuItem<String>(
            value: quality,
            child: _buildMenuItem(
              '$quality$bitrateText',
              _currentQuality == quality,
            ),
            onTap: () {
              setState(() {
                _currentQuality = quality;
              });
              _switchQuality(variant);
            },
          );
        }),
      ],
      elevation: 8.0,
    );
    _startHideControlsTimer();
  }

  void _showPlayPauseEffect(bool isPlaying) {
    setState(() {
      _showPlayPauseSplash = true;
    });
    _splashTimer?.cancel();
    _splashTimer = Timer(const Duration(milliseconds: 800), () {
      if (mounted) {
        setState(() {
          _showPlayPauseSplash = false;
        });
      }
    });
  }

  void _showVolumeEffect(double volume) {
    setState(() {
      _showVolumeSplash = true;
      _volumeSplashText = '${volume.round()}%';
    });
    _splashTimer?.cancel();
    _splashTimer = Timer(const Duration(milliseconds: 800), () {
      if (mounted) {
        setState(() {
          _showVolumeSplash = false;
        });
      }
    });
  }

  void _showCatSoundEffect() {
    final random = Random();
    final randomSound = _catSounds[random.nextInt(_catSounds.length)];

    setState(() {
      _showCatSound = true;
      _catSoundText = randomSound;
    });

    _catSoundTimer?.cancel();
    _catSoundTimer = Timer(const Duration(milliseconds: 1500), () {
      if (mounted) {
        setState(() {
          _showCatSound = false;
        });
      }
    });
  }

  void _switchQuality(hls_parser.Variant variant) {
    try {
      final currentPosition = player.state.position;
      final isPlaying = player.state.playing;

      player.open(Media(variant.url.toString()));

      player.seek(currentPosition);
      if (isPlaying) {
        player.play();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to switch quality: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _startChromecast() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.cast, color: Colors.blue),
              SizedBox(width: 8),
              Text('Cast to Device'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Select a device to cast to:'),
                const SizedBox(height: 16),
                ListTile(
                  leading: const Icon(Icons.tv, color: Colors.blue),
                  title: const Text('Living Room TV'),
                  subtitle: const Text('Chromecast'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _connectToChromecast('Living Room TV');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.tv, color: Colors.blue),
                  title: const Text('Bedroom TV'),
                  subtitle: const Text('Chromecast Ultra'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _connectToChromecast('Bedroom TV');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.speaker, color: Colors.green),
                  title: const Text('Google Home'),
                  subtitle: const Text('Audio only'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _connectToChromecast('Google Home');
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  void _connectToChromecast(String deviceName) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Connecting to $deviceName...'),
          backgroundColor: Colors.blue,
          action: SnackBarAction(
            label: 'Disconnect',
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Disconnected from cast device'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
          ),
        ),
      );
    }
  }

  void _toggleFullScreen() {
    setState(() {
      _isFullScreen = !_isFullScreen;
    });

    if (_isFullScreen) {
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: [],
      );
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    } else {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true, // Always allow popping the page
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop && _isFullScreen) {
          // If pop was not allowed and player is in full screen, exit full screen
          _toggleFullScreen();
        }
      },
      child: Scaffold(
        appBar:
            _isFullScreen
                ? null // Hide AppBar when player is in full screen
                : AppBar(
                  title: const Text('Player'),
                  leading: IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    tooltip: 'Back to Channels',
                  ),
                ),
        body: Focus(
          autofocus: true,
          onKeyEvent: (node, event) {
            if (event is KeyDownEvent &&
                event.logicalKey == LogicalKeyboardKey.f11) {
              _toggleFullScreen();
              return KeyEventResult.handled;
            }
            return KeyEventResult.ignored;
          },
          child:
              _isFullScreen
                  ? SizedBox.expand(
                    child: CustomPlayerSkin(
                      controller: controller,
                      hasSource: _hasSource,
                      showControls: _areControlsVisible,
                      isFullScreenMode: _isFullScreen,
                      availableQualities: _availableQualities,
                      isLiveStream: _isLiveStream,
                      bufferingPercentage: _bufferingPercentage,
                      errorMessage: _errorMessage,
                      isLoading: _isBuffering,
                      currentPosition: _currentPosition,
                      totalDuration: _totalDuration,
                      currentVolume: _currentVolume,
                      isPlaying: _isPlaying,
                      playbackSpeed: _playbackSpeed,
                      currentQuality: _currentQuality,
                      currentSubtitle: _currentSubtitle,
                      showPlayPauseSplash: _showPlayPauseSplash,
                      showVolumeSplash: _showVolumeSplash,
                      volumeSplashText: _volumeSplashText,
                      showCatSound: _showCatSound,
                      catSoundText: _catSoundText,
                      liveAnimation: _liveAnimation,
                      isVolumeSliderVisible: _isVolumeSliderVisible,
                      areControlsVisible: _areControlsVisible,
                      onPlayButtonPressed: () {
                        setState(() {
                          _areControlsVisible = true;
                        });
                        _startHideControlsTimer();
                      },
                      onTapVideo: () {
                        if (_hasSource) {
                          if (_isPlaying) {
                            player.pause();
                          } else {
                            player.play();
                          }
                          _showPlayPauseEffect(!_isPlaying);
                        }
                      },
                      onEnterControls: () {
                        setState(() {
                          _areControlsVisible = true;
                        });
                        _startHideControlsTimer();
                      },
                      onExitControls: () {
                        _hideControlsTimer?.cancel();
                        setState(() {
                          _areControlsVisible = false;
                        });
                      },
                      onSeek: (duration) {
                        player.seek(duration);
                      },
                      onSetVolume: (volume) {
                        player.setVolume(volume);
                        _showVolumeEffect(volume);
                      },
                      onToggleMute: () {
                        if (_currentVolume > 0) {
                          _previousVolume = _currentVolume;
                          player.setVolume(0);
                        } else {
                          player.setVolume(_previousVolume);
                        }
                      },
                      onSetRate: (rate) {
                        player.setRate(rate);
                        setState(() {
                          _playbackSpeed = rate;
                        });
                      },
                      onSwitchQuality: _switchQuality,
                      onShowSubtitlesMenu: _showSubtitlesMenu,
                      onShowSettingsMenu: _showSettingsMenu,
                      onEnterPictureInPicture: _enterPictureInPicture,
                      onToggleFullScreen: _toggleFullScreen,
                      onStartChromecast: _startChromecast,
                      onShowCatSoundEffect: _showCatSoundEffect,
                      onToggleVolumeSliderVisibility: (visible) {
                        setState(() {
                          _isVolumeSliderVisible = visible;
                        });
                      },
                    ),
                  )
                  : Center(
                    child: CustomPlayerSkin(
                      controller: controller,
                      hasSource: _hasSource,
                      showControls: _areControlsVisible,
                      isFullScreenMode: _isFullScreen,
                      availableQualities: _availableQualities,
                      isLiveStream: _isLiveStream,
                      bufferingPercentage: _bufferingPercentage,
                      errorMessage: _errorMessage,
                      isLoading: _isBuffering,
                      currentPosition: _currentPosition,
                      totalDuration: _totalDuration,
                      currentVolume: _currentVolume,
                      isPlaying: _isPlaying,
                      playbackSpeed: _playbackSpeed,
                      currentQuality: _currentQuality,
                      currentSubtitle: _currentSubtitle,
                      showPlayPauseSplash: _showPlayPauseSplash,
                      showVolumeSplash: _showVolumeSplash,
                      volumeSplashText: _volumeSplashText,
                      showCatSound: _showCatSound,
                      catSoundText: _catSoundText,
                      liveAnimation: _liveAnimation,
                      isVolumeSliderVisible: _isVolumeSliderVisible,
                      areControlsVisible: _areControlsVisible,
                      onPlayButtonPressed: () {
                        setState(() {
                          _areControlsVisible = true;
                        });
                        _startHideControlsTimer();
                      },
                      onTapVideo: () {
                        if (_hasSource) {
                          if (_isPlaying) {
                            player.pause();
                          } else {
                            player.play();
                          }
                          _showPlayPauseEffect(!_isPlaying);
                        }
                      },
                      onEnterControls: () {
                        setState(() {
                          _areControlsVisible = true;
                        });
                        _startHideControlsTimer();
                      },
                      onExitControls: () {
                        _hideControlsTimer?.cancel();
                        setState(() {
                          _areControlsVisible = false;
                        });
                      },
                      onSeek: (duration) {
                        player.seek(duration);
                      },
                      onSetVolume: (volume) {
                        player.setVolume(volume);
                        _showVolumeEffect(volume);
                      },
                      onToggleMute: () {
                        if (_currentVolume > 0) {
                          _previousVolume = _currentVolume;
                          player.setVolume(0);
                        } else {
                          player.setVolume(_previousVolume);
                        }
                      },
                      onSetRate: (rate) {
                        player.setRate(rate);
                        setState(() {
                          _playbackSpeed = rate;
                        });
                      },
                      onSwitchQuality: _switchQuality,
                      onShowSubtitlesMenu: _showSubtitlesMenu,
                      onShowSettingsMenu: _showSettingsMenu,
                      onEnterPictureInPicture: _enterPictureInPicture,
                      onToggleFullScreen: _toggleFullScreen,
                      onStartChromecast: _startChromecast,
                      onShowCatSoundEffect: _showCatSoundEffect,
                      onToggleVolumeSliderVisibility: (visible) {
                        setState(() {
                          _isVolumeSliderVisible = visible;
                        });
                      },
                    ),
                  ),
        ),
      ),
    );
  }
}
