import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as p;
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:sqflite_common_ffi_web/sqflite_ffi_web.dart';

class DatabaseLoader {
  // load database from assets located in assets/db
  static Future<Database> openPrebuiltDatabase() async {
    if (kIsWeb) {
      // For web, initialize FFI web and open the prebuilt database
      return await _openWebDatabase();
    } else {
      // For desktop/mobile platforms
      return await _openDesktopDatabase();
    }
  }

  static Future<Database> _openDesktopDatabase() async {
    final dbPath = await getDatabasesPath();
    final dbFile = File(p.join(dbPath, 'app.db'));
    if (kDebugMode) {
      print('Database path: ${dbFile.path}');
    }

    // Ensure the parent directory exists
    final parentDir = dbFile.parent;
    if (!await parentDir.exists()) {
      await parentDir.create(recursive: true);
    }

    if (!await dbFile.exists()) {
      // Copy the prebuilt database from assets
      final byteData = await rootBundle.load('assets/db/app.db');
      final buffer = byteData.buffer.asUint8List();
      await dbFile.writeAsBytes(buffer, flush: true);
    }

    return openDatabase(dbFile.path);
  }

  static Future<Database> _openWebDatabase() async {
    // Initialize FFI for web
    databaseFactory = databaseFactoryFfiWeb;

    // Open the database from the asset path
    return await databaseFactory.openDatabase(
      'assets/db/app.db', // Path to the database asset
      options: OpenDatabaseOptions(
        readOnly: true, // Assuming it's a read-only prebuilt database
        singleInstance: true,
      ),
    );
  }
}
