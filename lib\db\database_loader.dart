import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as p;
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

class DatabaseLoader {
  // load database from assets located in assets/db
  static Future<Database> openPrebuiltDatabase() async {
    if (kIsWeb) {
      // For web, initialize FFI web and open the prebuilt database
      return await _openWebDatabase();
    } else {
      // For desktop/mobile platforms
      return await _openDesktopDatabase();
    }
  }

  static Future<Database> _openDesktopDatabase() async {
    final dbPath = await getDatabasesPath();
    final dbFile = File(p.join(dbPath, 'app.db'));
    if (kDebugMode) {
      print('Database path: ${dbFile.path}');
    }

    // Ensure the parent directory exists
    final parentDir = dbFile.parent;
    if (!await parentDir.exists()) {
      await parentDir.create(recursive: true);
    }

    if (!await dbFile.exists()) {
      // Copy the prebuilt database from assets
      final byteData = await rootBundle.load('assets/db/app.db');
      final buffer = byteData.buffer.asUint8List();
      await dbFile.writeAsBytes(buffer, flush: true);
    }

    return openDatabase(dbFile.path);
  }

  static Future<Database> _openWebDatabase() async {
    // For web, we use the web database factory directly
    // The database will be created in browser storage (IndexedDB)

    try {
      if (kDebugMode) {
        print('Opening web database...');
      }

      final database = await databaseFactory.openDatabase(
        'app.db', // Database name for web storage
        options: OpenDatabaseOptions(
          version: 1,
          onCreate: (db, version) async {
            if (kDebugMode) {
              print('Creating new web database...');
            }
            await _createWebDatabaseSchema(db);
          },
        ),
      );

      if (kDebugMode) {
        print('Web database opened successfully');
      }

      return database;
    } catch (e) {
      if (kDebugMode) {
        print('Error opening web database: $e');
      }
      rethrow;
    }
  }

  static Future<void> _createWebDatabaseSchema(Database db) async {
    if (kDebugMode) {
      print('Creating database schema for web...');
    }

    // Create regions table
    await db.execute('''
      CREATE TABLE regions (
        code TEXT PRIMARY KEY,
        name TEXT NOT NULL
      )
    ''');

    // Create countries table
    await db.execute('''
      CREATE TABLE countries (
        code TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        flag TEXT
      )
    ''');

    // Create languages table
    await db.execute('''
      CREATE TABLE languages (
        code TEXT PRIMARY KEY,
        name TEXT NOT NULL
      )
    ''');

    // Create categories table
    await db.execute('''
      CREATE TABLE categories (
        category_id INTEGER PRIMARY KEY,
        name TEXT NOT NULL
      )
    ''');

    // Create channels table
    await db.execute('''
      CREATE TABLE channels (
        channel_id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        alt_names TEXT,
        country_code TEXT,
        category_id INTEGER,
        logo_url TEXT,
        is_active INTEGER DEFAULT 1,
        FOREIGN KEY (country_code) REFERENCES countries (code),
        FOREIGN KEY (category_id) REFERENCES categories (category_id)
      )
    ''');

    // Create channel_sources table
    await db.execute('''
      CREATE TABLE channel_sources (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        channel_id TEXT NOT NULL,
        source_url TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        FOREIGN KEY (channel_id) REFERENCES channels (channel_id)
      )
    ''');

    // Create country_languages table
    await db.execute('''
      CREATE TABLE country_languages (
        country_code TEXT NOT NULL,
        language_code TEXT NOT NULL,
        PRIMARY KEY (country_code, language_code),
        FOREIGN KEY (country_code) REFERENCES countries (code),
        FOREIGN KEY (language_code) REFERENCES languages (code)
      )
    ''');

    // Create region_countries table
    await db.execute('''
      CREATE TABLE region_countries (
        region_code TEXT NOT NULL,
        country_code TEXT NOT NULL,
        PRIMARY KEY (region_code, country_code),
        FOREIGN KEY (region_code) REFERENCES regions (code),
        FOREIGN KEY (country_code) REFERENCES countries (code)
      )
    ''');

    if (kDebugMode) {
      print('Database schema created successfully for web');
    }
  }
}
